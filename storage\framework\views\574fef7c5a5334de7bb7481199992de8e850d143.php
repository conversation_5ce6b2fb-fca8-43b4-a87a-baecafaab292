<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&family=Prompt:wght@300;400;500;600;700&family=Mitr:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Font Settings */
            --primary-font: 'Sarabun', sans-serif;
            --secondary-font: 'Kanit', sans-serif;
            --heading-font: 'Prompt', sans-serif;
            --base-font-size: 16px;
            --heading-font-size: 28px;
            --small-font-size: 14px;
            --line-height: 1.6;
            --font-weight: 400;
            --heading-weight: 600;

            /* Primary Colors - ธีมร้านก๋วยเตี๋ยวเรือ */
            --primary-color: #8B4513;      /* สีน้ำตาลเข้ม (ไม้เรือ) */
            --secondary-color: #D2691E;    /* สีน้ำตาลอ่อน */
            --accent-color: #CD853F;       /* สีน้ำตาลทอง */
            --gold-color: #DAA520;         /* สีทอง */

            /* Background Colors */
            --cream-color: #FFF8DC;        /* สีครีม */
            --light-cream: #FFFEF7;        /* สีครีมอ่อน */
            --warm-white: #FEFEFE;         /* สีขาวอบอุ่น */

            /* Text Colors */
            --text-dark: #2C1810;          /* สีข้อความเข้ม */
            --text-medium: #5D4E37;        /* สีข้อความกลาง */
            --text-light: #8B7355;         /* สีข้อความอ่อน */

            /* Boat Theme Colors */
            --boat-blue: #4682B4;          /* สีน้ำเงินเรือ */
            --water-blue: #87CEEB;         /* สีน้ำเงินน้ำ */
            --wood-brown: #A0522D;         /* สีน้ำตาลไม้ */

            /* Status Colors */
            --success-color: #28a745;      /* สีเขียว */
            --warning-color: #ffc107;      /* สีเหลือง */
            --danger-color: #dc3545;       /* สีแดง */
            --info-color: #17a2b8;         /* สีฟ้า */

            /* Shadow & Effects */
            --shadow-light: 0 2px 10px rgba(139, 69, 19, 0.1);
            --shadow-medium: 0 4px 20px rgba(139, 69, 19, 0.15);
            --shadow-heavy: 0 8px 30px rgba(139, 69, 19, 0.2);

            /* Border Radius */
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        body {
            font-family: var(--primary-font);
            background: linear-gradient(135deg, var(--light-cream) 0%, var(--cream-color) 100%);
            color: var(--text-dark);
            min-height: 100vh;
            line-height: var(--line-height);
            font-weight: var(--font-weight);
            font-size: var(--base-font-size);
        }

        /* Thai Restaurant Specific Font Styles */
        .restaurant-name, .hero-title {
            font-family: 'Prompt', 'Kanit', var(--heading-font) !important;
            font-weight: 600 !important;
        }

        .menu-title, .section-title {
            font-family: 'Kanit', var(--heading-font) !important;
            font-weight: 500 !important;
        }

        .description-text, .content-text {
            font-family: 'Sarabun', var(--primary-font) !important;
            line-height: 1.7 !important;
        }

        /* Heading Fonts */
        h1, h2, h3, h4, h5, h6,
        .h1, .h2, .h3, .h4, .h5, .h6 {
            font-family: var(--heading-font);
            font-weight: var(--heading-weight);
        }

        /* Small text */
        small, .small {
            font-size: var(--small-font-size);
        }

        /* Display headings */
        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-family: var(--heading-font);
            font-weight: var(--heading-weight);
        }

        /* Developer Tools Font Size Adjustment */
        * {
            -webkit-font-feature-settings: "kern" 1;
            font-feature-settings: "kern" 1;
            text-rendering: optimizeLegibility;
        }

        /* Reduce font size for better readability in dev tools */
        html {
            font-size: 14px; /* ลดจาก 16px เป็น 14px */
        }

        @media (min-width: 768px) {
            html {
                font-size: 15px; /* ขนาดกลางสำหรับ tablet */
            }
        }

        @media (min-width: 1200px) {
            html {
                font-size: 16px; /* ขนาดปกติสำหรับ desktop */
            }
        }

        /* Typography Improvements */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            color: var(--text-dark);
            line-height: 1.3;
        }

        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-weight: 700;
        }

        p {
            color: var(--text-medium);
            margin-bottom: 1rem;
        }

        .text-muted {
            color: var(--text-light) !important;
        }

        /* Link Styles */
        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        a:hover {
            color: var(--secondary-color);
        }
        
        /* Navigation Styles */
        .navbar {
            background: rgba(139, 69, 19, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 0.75rem 0;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(139, 69, 19, 0.95);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        }

        .navbar.scrolled {
            background: rgba(139, 69, 19, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-heavy);
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>');
            background-size: 200px 20px;
            background-repeat: repeat-x;
            background-position: bottom;
            pointer-events: none;
        }
        
        /* Navbar Brand */
        .navbar-brand {
            font-weight: 700;
            font-size: 1.6rem;
            color: white !important;
            display: flex;
            align-items: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: var(--gold-color) !important;
            transform: scale(1.05);
        }

        .navbar-brand img {
            height: 45px;
            width: auto;
            margin-right: 12px;
            border-radius: var(--border-radius-sm);
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover img {
            transform: rotate(5deg) scale(1.1);
        }

        /* Navigation Links */
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            margin: 0 8px;
            padding: 8px 16px !important;
            border-radius: var(--border-radius-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .navbar-nav .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .navbar-nav .nav-link:hover::before {
            left: 100%;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .navbar-nav .nav-link.active {
            background: rgba(218, 165, 32, 0.3);
            color: var(--gold-color) !important;
        }
        
        /* Dropdown Menu */
        .dropdown-menu {
            background: var(--warm-white);
            border: 2px solid var(--accent-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-heavy);
            padding: 1rem 0;
            min-width: 280px;
            max-width: 350px;
            max-height: 70vh;
            overflow-y: auto;
            margin-top: 8px;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            color: var(--text-dark);
            font-weight: 500;
            padding: 12px 20px;
            transition: all 0.3s ease;
            border-radius: 0;
            position: relative;
            display: flex;
            align-items: center;
        }

        .dropdown-item i {
            width: 20px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            transform: translateX(8px);
            margin: 0 8px;
            border-radius: var(--border-radius-sm);
        }

        .dropdown-item:hover i {
            transform: scale(1.2);
        }

        .dropdown-divider {
            border-color: var(--accent-color);
            opacity: 0.3;
            margin: 8px 0;
        }

        .dropdown-item.ps-4:hover {
            padding-left: 2.5rem;
        }

        .dropdown-item.ps-5:hover {
            padding-left: 3rem;
        }

        .dropdown-header {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.5rem 1.5rem;
        }

        .dropdown-divider {
            border-color: var(--accent-color);
            opacity: 0.3;
            margin: 0.5rem 0;
        }

        /* Custom scrollbar for dropdown */
        .dropdown-menu::-webkit-scrollbar {
            width: 6px;
        }

        .dropdown-menu::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 3px;
        }

        .dropdown-menu::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .dropdown-menu::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
        
        /* Button Styles */
        .btn {
            border-radius: var(--border-radius-md);
            font-weight: 500;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .btn-lg {
            padding: 16px 32px;
            font-size: 1.1rem;
            border-radius: var(--border-radius-lg);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
            border-radius: var(--border-radius-sm);
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            background: var(--warm-white);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .card-header {
            background: linear-gradient(135deg, var(--cream-color), var(--light-cream));
            border-bottom: 2px solid var(--accent-color);
            font-weight: 600;
            color: var(--text-dark);
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .card-text {
            color: var(--text-medium);
            line-height: 1.6;
        }
        
        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, var(--text-dark) 0%, var(--primary-color) 50%, var(--wood-brown) 100%);
            color: white;
            padding: 60px 0 30px;
            margin-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--gold-color), var(--accent-color), var(--gold-color));
        }

        .footer h5 {
            color: var(--gold-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .footer h5::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 50px;
            height: 2px;
            background: var(--accent-color);
        }

        .footer p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 0.5rem;
        }

        .footer i {
            color: var(--gold-color);
            margin-right: 8px;
            width: 20px;
        }

        .footer hr {
            border-color: rgba(255, 255, 255, 0.2);
            margin: 2rem 0;
        }

        .footer .text-center p {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        /* Footer Enhancements */
        .footer .contact-info p {
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .footer .contact-info p:hover {
            color: var(--gold-color);
            transform: translateX(5px);
        }

        .footer ul li {
            margin-bottom: 0.5rem;
        }

        .footer ul li a {
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .hover-gold:hover {
            color: var(--gold-color) !important;
            transform: translateX(3px);
        }

        .footer .fab, .footer .fas {
            transition: all 0.3s ease;
        }

        .footer .fab:hover, .footer .fas:hover {
            transform: scale(1.2);
        }
        
        .hero-section {
            background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(210, 105, 30, 0.7)),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 300"><path fill="%23F5F5DC" d="M0,100 C150,200 350,0 500,100 C650,200 850,0 1000,100 L1000,300 L0,300 Z"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            min-height: 30vh;
            display: flex;
            align-items: center;
            text-align: center;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 1px solid rgba(139, 69, 19, 0.1);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: var(--gold-color);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-bottom: none;
            padding: 1.25rem 1.5rem;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 992px) {
            .display-2 {
                font-size: 2.8rem;
            }

            .display-3 {
                font-size: 2.2rem;
            }

            .display-4 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.3rem;
            }

            .navbar-brand img {
                height: 35px;
            }

            .navbar-nav .nav-link {
                margin: 4px 0;
                padding: 12px 16px !important;
                text-align: center;
                border-radius: var(--border-radius-sm);
                background: rgba(255, 255, 255, 0.1);
                margin-bottom: 4px;
            }

            .navbar-collapse {
                background: rgba(139, 69, 19, 0.95);
                margin-top: 1rem;
                padding: 1rem;
                border-radius: var(--border-radius-md);
                backdrop-filter: blur(10px);
            }

            .dropdown-menu {
                min-width: 100%;
                margin-top: 0;
                border-radius: var(--border-radius-md);
                background: rgba(255, 255, 255, 0.98);
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .btn-lg {
                padding: 14px 28px;
                font-size: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .footer {
                padding: 40px 0 20px;
                margin-top: 60px;
            }

            .footer h5 {
                margin-bottom: 1rem;
                font-size: 1.1rem;
            }

            .footer .contact-info p {
                font-size: 0.9rem;
            }

            .hero-section {
                min-height: 50vh !important;
            }

            .display-2 {
                font-size: 2.2rem;
            }

            .display-3 {
                font-size: 1.8rem;
            }

            .display-4 {
                font-size: 1.5rem;
            }

            .lead {
                font-size: 1.1rem;
            }

            .fs-3 {
                font-size: 1.3rem !important;
            }

            .fs-4 {
                font-size: 1.1rem !important;
            }

            .fs-5 {
                font-size: 1rem !important;
            }

            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .gap-4 {
                gap: 1rem !important;
            }

            .py-5 {
                padding-top: 2rem !important;
                padding-bottom: 2rem !important;
            }
        }

        @media (max-width: 576px) {
            .navbar-brand {
                font-size: 1.1rem;
            }

            .navbar-brand img {
                height: 30px;
                margin-right: 8px;
            }

            .btn {
                padding: 8px 16px;
                font-size: 0.85rem;
            }

            .card-body {
                padding: 0.75rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }

        .fade-in-left {
            animation: fadeInLeft 0.6s ease forwards;
        }

        .fade-in-right {
            animation: fadeInRight 0.6s ease forwards;
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* Enhanced Loading States */
        .loading {
            position: relative;
            overflow: hidden;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Page loader removed for better performance */

        /* Button Loading Effects - Hidden */
        .btn-loading {
            position: relative;
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.7);
            border-radius: inherit;
        }

        /* Smooth page transitions */
        .page-content {
            opacity: 0;
            animation: fadeIn 0.3s ease-out forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* Loading screen with wood background */
        .page-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('/images/restaurant/รูปไม้.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 1;
            transition: all 0.8s ease;
        }

        .page-loading-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1;
        }

        .page-loading-overlay.slide-up {
            transform: translateY(-100%);
            opacity: 0;
            pointer-events: none;
        }

        .loading-content {
            text-align: center;
            color: white;
            position: relative;
            z-index: 2;
            background: rgba(0, 0, 0, 0.7);
            padding: 30px 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .loading-text {
            font-size: 1.4rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* Welcome popup */
        .welcome-popup {
            position: fixed;
            bottom: -300px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg,
                rgba(139, 69, 19, 0.95) 0%,
                rgba(218, 165, 32, 0.95) 100%);
            color: white;
            padding: 25px 35px;
            border-radius: 15px 15px 0 0;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.3);
            z-index: 9998;
            transition: bottom 0.8s ease;
            text-align: center;
            min-width: 300px;
        }

        .welcome-popup.show {
            bottom: 0;
        }

        .welcome-popup h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .welcome-popup p {
            margin: 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        .loading-dots {
            display: inline-block;
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        /* Loading placeholder for images */
        .img-loading {
            position: relative;
            background: #e9ecef;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .img-loading::before {
            content: 'กำลังเตรียมรูปภาพ...';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #495057;
            font-size: 1rem;
            font-weight: 500;
            text-align: center;
            z-index: 1;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 16px;
            border-radius: 6px;
        }

        /* Image Loading Placeholder */
        .img-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        /* Smooth Transitions */
        * {
            transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-medium);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }
        
        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 30px;
            position: relative;
            text-align: center;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--gold-color));
            border-radius: 2px;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .btn {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: none;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .badge {
            border-radius: 20px;
            padding: 0.5em 1em;
            font-weight: 500;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, var(--light-brown), var(--cream-color));
            color: var(--text-dark);
            font-weight: 600;
            border: none;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-color: rgba(139, 69, 19, 0.1);
        }

        .table tbody tr:hover {
            background-color: rgba(218, 165, 32, 0.1);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .alert {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid rgba(139, 69, 19, 0.2);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--gold-color);
            box-shadow: 0 0 0 0.2rem rgba(218, 165, 32, 0.25);
        }

        .breadcrumb {
            background: rgba(255,255,255,0.8);
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            backdrop-filter: blur(10px);
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--primary-color);
            font-weight: bold;
        }

        /* Hero Section Styles */
        .hero-section {
            min-height: 100vh;
            height: 100vh;
            position: relative;
            overflow: hidden;
            width: 100vw;
            margin: 0;
            padding: 0;
        }

        .hero-slide {
            height: 100vh;
            width: 100vw;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 0;
        }

        .hero-section .container-fluid {
            max-width: none;
            width: 100%;
            padding: 0;
            margin: 0;
        }

        /* Transparent navbar on hero section */
        body.hero-page .navbar {
            background: rgba(139, 69, 19, 0.2) !important;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.hero-page .navbar.scrolled {
            background: rgba(139, 69, 19, 0.95) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Navbar text color for hero page */
        body.hero-page .navbar .navbar-brand,
        body.hero-page .navbar .nav-link {
            color: white !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        body.hero-page .navbar .nav-link:hover {
            color: #ffd700 !important;
        }

        /* Ensure full width for hero page */
        body.hero-page {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        body.hero-page main {
            padding: 0;
            margin: 0;
        }

        /* Hero Content */
        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            margin-bottom: 1.5rem;
            line-height: 1.2;
            color: #ffffff;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            font-weight: 400;
            color: #f8f9fa;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .hero-tagline {
            font-size: 1.2rem;
            color: #e9ecef;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
            font-style: italic;
            margin-bottom: 2rem;
        }

        /* Hero Buttons */
        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-buttons .btn {
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-buttons .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
        }

        .hero-buttons .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            border: none;
            color: #000;
        }

        .hero-buttons .btn-warning:hover {
            color: #000;
            box-shadow: 0 12px 25px rgba(255, 193, 7, 0.6);
        }

        .hero-buttons .btn-outline-light {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            backdrop-filter: blur(10px);
        }

        .hero-buttons .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: #fff;
        }

        /* Responsive Design for Hero */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .hero-tagline {
                font-size: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-buttons .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <style>
    /* Custom SVG Icon Classes */
    .icon-boat-noodle {
        display: inline-block;
        width: 1em;
        height: 1em;
        background-image: url('<?php echo e(asset('images/icons/boat-noodle-simple.svg')); ?>');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        vertical-align: middle;
    }

    .icon-boat-noodle-lg {
        width: 2em;
        height: 2em;
    }

    .icon-boat-noodle-xl {
        width: 3em;
        height: 3em;
    }

    .icon-boat-noodle-2x {
        width: 2em;
        height: 2em;
    }

    .icon-boat-noodle-3x {
        width: 3em;
        height: 3em;
    }

    .icon-boat-noodle-4x {
        width: 4em;
        height: 4em;
    }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="page-loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p class="loading-text">กำลังเตรียม<span class="loading-dots">...</span></p>
        </div>
    </div>

    <!-- Welcome Popup -->
    <div class="welcome-popup" id="welcomePopup">
        <h3><i class="fas fa-bowl-food me-2"></i>ยินดีต้อนรับ</h3>
        <p>ร้านก๋วยเตี๋ยวเรือเข้าท่า</p>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                <?php
                    $restaurantInfo = \App\Models\RestaurantInfo::getInfo();
                ?>
                <?php if($restaurantInfo->logo): ?>
                    <img src="<?php echo e(asset('storage/' . $restaurantInfo->logo)); ?>" alt="<?php echo e($restaurantInfo->name ?? 'ก๋วยเตี๋ยวเรือเข้าท่า'); ?>" style="height: 40px; width: auto;" class="me-2">
                <?php else: ?>
                    <span class="icon-boat-noodle me-2"></span>
                <?php endif; ?>
                <?php echo e($restaurantInfo->name ?? 'ก๋วยเตี๋ยวเรือเข้าท่า'); ?>

            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('home')); ?>">หน้าหลัก</a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="menuDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-utensils me-1"></i>เมนูอาหาร
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="menuDropdown">
                            <?php
                                $navCategories = \App\Models\Category::active()->ordered()->take(6)->get();
                            ?>

                            <?php $__currentLoopData = $navCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $navCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('menu.category', $navCategory->slug)); ?>">
                                    <?php if($navCategory->icon): ?>
                                        <?php if(str_contains($navCategory->icon, 'icon-boat-noodle')): ?>
                                            <span class="<?php echo e($navCategory->icon); ?> me-2"></span>
                                        <?php else: ?>
                                            <i class="<?php echo e($navCategory->icon); ?> me-2 text-primary"></i>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="icon-boat-noodle me-2"></span>
                                    <?php endif; ?>
                                    <?php echo e($navCategory->name); ?>

                                </a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center fw-bold text-primary" href="<?php echo e(route('menu.index')); ?>">
                                <i class="fas fa-list me-2"></i>ดูเมนูทั้งหมด
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('news.index')); ?>">
                            <i class="fas fa-newspaper me-1"></i>ข่าวสาร
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('about')); ?>">
                            <i class="fas fa-info-circle me-1"></i>เกี่ยวกับ
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('contact.index')); ?>">
                            <i class="fas fa-phone me-1"></i>ติดต่อเรา
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(Auth::user()->isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('admin.dashboard')); ?>">
                                    <i class="fas fa-cog me-1"></i>จัดการระบบ
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i><?php echo e(Auth::user()->name); ?>

                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li>
                                    <form action="<?php echo e(route('logout')); ?>" method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('login')); ?>">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="padding-top: 0;">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row g-4">
                <!-- Restaurant Info -->
                <div class="col-lg-4 col-md-6">
                    <?php
                        $footerRestaurantInfo = \App\Models\RestaurantInfo::getInfo();
                    ?>
                    <h5>
                        <span class="icon-boat-noodle me-2"></span>
                        <?php echo e($footerRestaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

                    </h5>
                    <p class="mb-3"><?php echo e($footerRestaurantInfo->description ?? 'ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม ด้วยน้ำซุปเข้มข้น เครื่องเทศครบเครื่อง อร่อยถูกปากทุกคน'); ?></p>
                    <div class="d-flex gap-3">
                        <?php if($footerRestaurantInfo->facebook): ?>
                            <a href="<?php echo e($footerRestaurantInfo->facebook); ?>" target="_blank" class="text-white-50 hover-gold" title="Facebook">
                                <i class="fab fa-facebook-f fa-lg"></i>
                            </a>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->line): ?>
                            <a href="https://line.me/ti/p/<?php echo e(ltrim($footerRestaurantInfo->line, '@')); ?>" target="_blank" class="text-white-50 hover-gold" title="Line">
                                <i class="fab fa-line fa-lg"></i>
                            </a>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->instagram): ?>
                            <a href="https://instagram.com/<?php echo e(ltrim($footerRestaurantInfo->instagram, '@')); ?>" target="_blank" class="text-white-50 hover-gold" title="Instagram">
                                <i class="fab fa-instagram fa-lg"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-4 col-md-6">
                    <h5>ติดต่อเรา</h5>
                    <div class="contact-info">
                        <?php if($footerRestaurantInfo->address): ?>
                            <p><i class="fas fa-map-marker-alt me-2"></i><?php echo e($footerRestaurantInfo->address); ?></p>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->phone): ?>
                            <p><i class="fas fa-phone me-2"></i><?php echo e($footerRestaurantInfo->phone); ?></p>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->mobile): ?>
                            <p><i class="fas fa-mobile-alt me-2"></i><?php echo e($footerRestaurantInfo->mobile); ?></p>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->open_time && $footerRestaurantInfo->close_time): ?>
                            <p><i class="fas fa-clock me-2"></i>เปิดทุกวัน <?php echo e($footerRestaurantInfo->open_time); ?> - <?php echo e($footerRestaurantInfo->close_time); ?> น.</p>
                        <?php endif; ?>
                        <?php if($footerRestaurantInfo->email): ?>
                            <p><i class="fas fa-envelope me-2"></i><?php echo e($footerRestaurantInfo->email); ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-4 col-md-12">
                    <h5>ลิงก์ด่วน</h5>
                    <div class="row">
                        <div class="col-6">
                            <ul class="list-unstyled">
                                <li><a href="<?php echo e(route('home')); ?>" class="text-white-50 hover-gold"><i class="fas fa-home me-2"></i>หน้าหลัก</a></li>
                                <li><a href="<?php echo e(route('menu.index')); ?>" class="text-white-50 hover-gold"><i class="fas fa-utensils me-2"></i>เมนูอาหาร</a></li>
                                <li><a href="<?php echo e(route('news.index')); ?>" class="text-white-50 hover-gold"><i class="fas fa-newspaper me-2"></i>ข่าวสาร</a></li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="list-unstyled">
                                <li><a href="<?php echo e(route('about')); ?>" class="text-white-50 hover-gold"><i class="fas fa-info-circle me-2"></i>เกี่ยวกับเรา</a></li>
                                <li><a href="<?php echo e(route('contact.index')); ?>" class="text-white-50 hover-gold"><i class="fas fa-phone me-2"></i>ติดต่อเรา</a></li>
                                <?php if(auth()->guard()->check()): ?>
                                    <?php if(Auth::user()->isAdmin()): ?>
                                        <li><a href="<?php echo e(route('admin.dashboard')); ?>" class="text-white-50 hover-gold"><i class="fas fa-cog me-2"></i>จัดการระบบ</a></li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo e(date('Y')); ?> <?php echo e($footerRestaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>. สงวนลิขสิทธิ์.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>
                            <i class="fas fa-heart text-danger me-1"></i>
                            สร้างด้วยความใส่ใจ |
                            <a href="#" class="text-white-50 hover-gold">นโยบายความเป็นส่วนตัว</a>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CSRF Token Setup -->
    <script>
        // Setup CSRF token for all AJAX requests
        window.Laravel = {
            csrfToken: '<?php echo e(csrf_token()); ?>'
        };

        // Setup axios defaults if using axios
        if (typeof axios !== 'undefined') {
            axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }

        // Setup jQuery AJAX defaults if using jQuery
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        }
    </script>

    <!-- Enhanced UX/UI JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navbar scroll effect
            const navbar = document.querySelector('.navbar');
            let lastScrollTop = 0;

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                lastScrollTop = scrollTop;
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading animation to buttons on click
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('loading')) {
                        this.classList.add('loading');
                        setTimeout(() => {
                            this.classList.remove('loading');
                        }, 2000);
                    }
                });
            });

            // Intersection Observer for fade-in animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-up');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.card, .section-title, .hero-section').forEach(el => {
                observer.observe(el);
            });

            // Enhanced dropdown behavior
            document.querySelectorAll('.dropdown').forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');

                if (toggle && menu) {
                    toggle.addEventListener('mouseenter', function() {
                        if (window.innerWidth > 768) {
                            menu.classList.add('show');
                        }
                    });

                    dropdown.addEventListener('mouseleave', function() {
                        if (window.innerWidth > 768) {
                            menu.classList.remove('show');
                        }
                    });
                }
            });

            // Add ripple effect to buttons
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // Loading Effects with Wood Background and Popup
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const welcomePopup = document.getElementById('welcomePopup');

            // Check if we're on the home page
            const isHomePage = window.location.pathname === '/' || window.location.pathname === '/home';

            if (loadingOverlay) {
                setTimeout(() => {
                    // Slide up loading screen
                    loadingOverlay.classList.add('slide-up');

                    // Show welcome popup only on home page
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                        if (welcomePopup && isHomePage) {
                            welcomePopup.classList.add('show');

                            // Hide welcome popup after 3 seconds
                            setTimeout(() => {
                                welcomePopup.classList.remove('show');
                            }, 3000);
                        }
                    }, 800);
                }, 1200);
            }

            // Add page content fade-in
            const pageContent = document.querySelector('main, .container, .hero-section');
            if (pageContent) {
                pageContent.classList.add('page-content');
            }

            // Button loading states (very subtle)
            document.querySelectorAll('a[href]:not([href^="#"]), button[type="submit"]').forEach(element => {
                element.addEventListener('click', function(e) {
                    // Skip for external links and anchors
                    if (this.target === '_blank' ||
                        this.href?.includes('http') && !this.href.includes(window.location.hostname) ||
                        this.href?.includes('#')) {
                        return;
                    }

                    // Add very subtle loading state (just opacity change)
                    this.classList.add('btn-loading');

                    // Remove loading state very quickly
                    setTimeout(() => {
                        this.classList.remove('btn-loading');
                    }, 800);
                });
            });

            // Image loading effects (minimal)
            document.querySelectorAll('img').forEach(img => {
                if (!img.complete && !img.src.includes('data:')) {
                    img.classList.add('img-loading');
                    img.addEventListener('load', function() {
                        this.classList.remove('img-loading');
                    });
                    // Remove loading class after timeout
                    setTimeout(() => {
                        img.classList.remove('img-loading');
                    }, 3000);
                }
            });
        });

        // Scroll to Top Button
        const scrollToTopBtn = document.getElementById('scrollToTop');

        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.classList.add('show');
            } else {
                scrollToTopBtn.classList.remove('show');
            }
        });

        // Add ripple CSS
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            .btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // Scroll to Top Function
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Make function global
        window.scrollToTop = scrollToTop;

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/layouts/app.blade.php ENDPATH**/ ?>